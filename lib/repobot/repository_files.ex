defmodule Repobot.RepositoryFiles do
  @moduledoc """
  The RepositoryFiles context.
  """

  require Logger

  import Ecto.Query, warn: false

  alias Repobot.Repo
  alias Repobot.RepositoryFile

  @doc """
  Returns the list of repository files for a given repository.
  """
  def list_repository_files(repository) do
    RepositoryFile
    |> where(repository_id: ^repository.id)
    |> Repo.all()
  end

  @doc """
  Gets a single repository file.
  Raises `Ecto.NoResultsError` if the Repository file does not exist.
  """
  def get_repository_file!(id), do: Repo.get!(RepositoryFile, id)

  @doc """
  Gets a single repository file by repository_id and path.
  Returns nil if the file does not exist.
  """
  def get_repository_file_by_path(repository_id, path) do
    RepositoryFile
    |> where(repository_id: ^repository_id, path: ^path)
    |> Repo.one()
  end

  @doc """
  Creates a repository file.
  """
  def create_repository_file(attrs \\ %{}) do
    %RepositoryFile{}
    |> RepositoryFile.changeset(attrs)
    |> Repo.insert()
  end

  @doc """
  Updates a repository file.
  """
  def update_repository_file(%RepositoryFile{} = repository_file, attrs) do
    repository_file
    |> RepositoryFile.changeset(attrs)
    |> Repo.update()
  end

  @doc """
  Deletes a repository file.
  """
  def delete_repository_file(%RepositoryFile{} = repository_file) do
    Repo.delete(repository_file)
  end

  @doc """
  Returns an `%Ecto.Changeset{}` for tracking repository file changes.
  """
  def change_repository_file(%RepositoryFile{} = repository_file, attrs \\ %{}) do
    RepositoryFile.changeset(repository_file, attrs)
  end

  @doc """
  Syncs repository files with github_api().
  This will create, update, or delete repository files based on the GitHub tree.
  Uses the efficient Git Trees API to minimize API calls.
  """
  def sync_repository_files(repository, user) do
    client = github_api().client(user)

    # First, get the current tree SHA to check if we need to update
    case get_current_tree_sha(client, repository) do
      {:ok, current_tree_sha} ->
        if repository.tree_sha == current_tree_sha do
          Logger.info("Tree SHA unchanged for #{repository.full_name}, skipping sync")
          {:ok, []}
        else
          Logger.info(
            "Tree SHA changed for #{repository.full_name} (#{repository.tree_sha} -> #{current_tree_sha}), syncing..."
          )

          sync_with_tree_sha(client, repository, current_tree_sha)
        end

      {:error, reason} ->
        Logger.warning(
          "Failed to get current tree SHA for #{repository.full_name}: #{reason}, proceeding with sync"
        )

        sync_without_tree_sha(client, repository)
    end
  end

  defp get_current_tree_sha(client, repository) do
    case github_api().get_repo(client, repository.owner, repository.name) do
      {200, repo_data, _} ->
        default_branch = repo_data["default_branch"]

        case github_api().get_ref(
               client,
               repository.owner,
               repository.name,
               "heads/#{default_branch}"
             ) do
          {200, %{"object" => %{"sha" => commit_sha}}, _} ->
            case github_api().get_commit(client, repository.owner, repository.name, commit_sha) do
              {200, %{"tree" => %{"sha" => tree_sha}}, _} ->
                {:ok, tree_sha}

              {status, body, _} ->
                {:error, "Failed to get commit tree: #{status} - #{inspect(body)}"}
            end

          {status, body, _} ->
            {:error, "Failed to get branch ref: #{status} - #{inspect(body)}"}
        end

      {status, body, _} ->
        {:error, "Failed to get repository info: #{status} - #{inspect(body)}"}
    end
  end

  defp sync_with_tree_sha(client, repository, tree_sha) do
    case github_api().get_tree_efficient(client, repository.owner, repository.name, tree_sha) do
      {:ok, tree} ->
        Logger.info(
          "Fetched tree for #{repository.full_name} using efficient Git Trees API (#{length(tree)} items)"
        )

        result = process_tree_sync(repository, tree)

        # Update the repository with the new tree SHA
        case result do
          {:ok, files} ->
            update_repository_tree_info(repository, tree_sha)
            {:ok, files}

          error ->
            error
        end

      {:error, reason} ->
        Logger.warning(
          "Efficient tree fetch failed for #{repository.full_name}: #{reason}, falling back to Contents API"
        )

        sync_without_tree_sha(client, repository)
    end
  end

  defp sync_without_tree_sha(client, repository) do
    case github_api().get_tree(client, repository.owner, repository.name) do
      {:ok, tree} ->
        Logger.info(
          "Fetched tree for #{repository.full_name} using Contents API (#{length(tree)} items)"
        )

        process_tree_sync(repository, tree)

      {:error, reason} ->
        Logger.error("Failed to get tree from GitHub: #{inspect(reason)}")
        {:error, reason}
    end
  end

  defp process_tree_sync(repository, tree) do
    Logger.debug("Got tree from GitHub with #{length(tree)} items")

    # Get existing files for this repository
    existing_files =
      RepositoryFile
      |> where(repository_id: ^repository.id)
      |> Repo.all()
      |> Enum.group_by(& &1.path)

    Logger.debug("Found #{map_size(existing_files)} existing files in database")

    # Create a set of paths from the GitHub tree
    github_paths = MapSet.new(tree, & &1["path"])
    Logger.debug("GitHub paths: #{inspect(github_paths)}")

    # Find files that exist in our database but not in GitHub anymore
    files_to_delete =
      existing_files
      |> Map.keys()
      |> Enum.reject(&MapSet.member?(github_paths, &1))
      |> Enum.flat_map(&Map.get(existing_files, &1, []))

    Logger.debug("Files to delete: #{inspect(Enum.map(files_to_delete, & &1.path))}")

    # Start a transaction to ensure all operations succeed or fail together
    case Repo.transaction(fn ->
           # Delete files that no longer exist in GitHub
           Enum.each(files_to_delete, fn file ->
             Logger.debug("Deleting file: #{file.path}")

             case Repo.delete(file) do
               {:ok, _} -> :ok
               {:error, reason} -> Repo.rollback("Failed to delete file: #{inspect(reason)}")
             end
           end)

           # Process each file in the tree
           results =
             tree
             |> Enum.map(fn item ->
               Logger.debug("Processing tree item: #{inspect(item)}")
               process_tree_item(item, repository, existing_files)
             end)

           case handle_tree_processing_results(results) do
             {:ok, files} ->
               Logger.debug("Successfully processed #{length(files)} files")
               {:ok, files}

             {:error, reason} ->
               Logger.error("Failed to process tree: #{reason}")
               Repo.rollback(reason)
           end
         end) do
      {:ok, {:ok, files}} ->
        Logger.debug("Transaction completed successfully with #{length(files)} files")
        {:ok, files}

      {:ok, files} ->
        Logger.debug(
          "Transaction completed successfully (alt format) with #{length(files)} files"
        )

        {:ok, files}

      {:error, reason} ->
        Logger.error("Transaction failed: #{inspect(reason)}")
        {:error, reason}
    end
  end

  defp update_repository_tree_info(repository, tree_sha) do
    Repobot.Repositories.update_repository(repository, %{
      tree_sha: tree_sha,
      tree_updated_at: DateTime.utc_now() |> DateTime.truncate(:second)
    })
  end

  defp process_tree_item(item, repository, existing_files) do
    case Map.get(existing_files, item["path"]) do
      [existing_file] ->
        # Update existing file if SHA changed
        if existing_file.sha != item["sha"] do
          Logger.debug("Updating existing file #{item["path"]} with new SHA")

          update_repository_file(existing_file, %{
            sha: item["sha"],
            size: item["size"],
            # Clear content since it needs to be refetched
            content_updated_at: nil
          })
        else
          Logger.debug("File #{item["path"]} unchanged")
          {:ok, existing_file}
        end

      nil ->
        Logger.debug("Creating new file #{item["path"]}")
        # Create new file
        create_repository_file(%{
          repository_id: repository.id,
          path: item["path"],
          name: Path.basename(item["path"]),
          type: item["type"],
          size: item["size"],
          sha: item["sha"]
        })

      _ ->
        Logger.error("Multiple files found with path #{item["path"]}")
        {:error, "Multiple files found with the same path"}
    end
  end

  defp handle_tree_processing_results(results) do
    {successes, failures} =
      results
      |> Enum.split_with(fn
        {:ok, _} -> true
        _ -> false
      end)

    case failures do
      [] -> {:ok, Enum.map(successes, fn {:ok, file} -> file end)}
      _ -> {:error, "Failed to process some files"}
    end
  end

  @doc """
  Fetches the content of a repository file from github_api().
  """
  def fetch_file_content(%RepositoryFile{} = repository_file, user) do
    repository = Repo.get!(Repobot.Repository, repository_file.repository_id)
    client = github_api().client(user)

    case github_api().get_file_content(
           client,
           repository.owner,
           repository.name,
           repository_file.path
         ) do
      {:ok, content, _response} ->
        update_repository_file(repository_file, %{
          content: content,
          content_updated_at: DateTime.utc_now() |> DateTime.truncate(:second)
        })

      {:error, reason} ->
        {:error, reason}
    end
  end

  @doc """
  Gets repository files at a specific path.
  For directories, this returns the immediate children.
  For files, this returns the file itself.
  """
  def get_files_at_path(repository, path) do
    normalized_path = String.trim_leading(path, "/")

    # For root path, get files with no "/" in their path
    if path == "/" do
      RepositoryFile
      |> where(repository_id: ^repository.id)
      |> where([f], not like(f.path, "%/%"))
      |> Repo.all()
    else
      # For other paths, get the exact file or immediate children
      parent_path = normalized_path <> "/"

      RepositoryFile
      |> where(repository_id: ^repository.id)
      |> where([f], f.path == ^normalized_path or like(f.path, ^(parent_path <> "%")))
      |> where([f], not like(f.path, ^(parent_path <> "%/%")))
      |> Repo.all()
    end
  end

  def github_api() do
    Application.get_env(:repobot, :github_api)
  end
end
