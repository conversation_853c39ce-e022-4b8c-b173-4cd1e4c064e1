defmodule RepobotWeb.Live.Onboarding.IndexTest do
  use RepobotWeb.ConnCase, async: true
  use Repobot.Test.Fixtures

  import Phoenix.LiveViewTest
  import Mox

  alias Repobot.Test.GitHubMock
  alias Repobot.Accounts.User.Settings

  setup :verify_on_exit!

  defp debug_html(view, _msg) do
    _html = render(view)
    view
  end

  describe "onboarding" do
    setup do
      user = create_user()
      user = %{user | settings: %Settings{onboarding_completed: false}}

      # Repositories
      template_repo =
        create_repository(%{
          name: "template-repo",
          owner: user.login,
          full_name: "#{user.login}/template-repo",
          organization_id: user.default_organization_id,
          data: %{"default_branch" => "main"}
        })

      sync_repo_common =
        create_repository(%{
          name: "sync-repo-common",
          owner: user.login,
          full_name: "#{user.login}/sync-repo-common",
          organization_id: user.default_organization_id
        })

      sync_repo_distinct =
        create_repository(%{
          name: "sync-repo-distinct",
          owner: user.login,
          full_name: "#{user.login}/sync-repo-distinct",
          organization_id: user.default_organization_id
        })

      # Folder setup
      folder =
        create_folder(%{
          name: "Test Folder",
          user_id: user.id,
          organization_id: user.default_organization_id
        })

      {:ok, sync_repo_common} =
        Repobot.Repositories.update_repository(sync_repo_common, %{folder_id: folder.id})

      # --- File Creation ---
      common_content = "This is the common content."

      create_repository_file(%{
        path: "template_only.txt",
        content: "Template specific file.",
        size: 23,
        type: "file",
        sha: "template_sha",
        repository_id: template_repo.id
      })

      create_repository_file(%{
        path: "common.txt",
        # Identical content
        content: common_content,
        size: String.length(common_content),
        type: "file",
        # Can be same or different sha, content matters
        sha: "common123",
        repository_id: sync_repo_common.id
      })

      create_repository_file(%{
        path: "sync_common_only.txt",
        content: "Sync common specific file.",
        size: 28,
        type: "file",
        sha: "sync_common_sha",
        repository_id: sync_repo_common.id
      })

      create_repository_file(%{
        path: "distinct_only.txt",
        content: "Distinct specific file.",
        size: 25,
        type: "file",
        sha: "distinct_sha",
        repository_id: sync_repo_distinct.id
      })

      # Add common.txt to sync_repo_distinct as well for the second test
      create_repository_file(%{
        path: "common.txt",
        content: common_content,
        size: String.length(common_content),
        type: "file",
        # Can be same or different sha
        sha: "common123",
        repository_id: sync_repo_distinct.id
      })

      # Preload files
      template_repo = Repobot.Repo.preload(template_repo, :files)
      sync_repo_common = Repobot.Repo.preload(sync_repo_common, [:files, :folder])
      sync_repo_distinct = Repobot.Repo.preload(sync_repo_distinct, :files)

      # Verify file counts
      assert length(template_repo.files) == 1
      assert length(sync_repo_common.files) == 2
      assert length(sync_repo_distinct.files) == 2

      {
        :ok,
        # Pass common content for mock verification
        user: user,
        template_repo: template_repo,
        sync_repo_common: sync_repo_common,
        sync_repo_distinct: sync_repo_distinct,
        folder: folder,
        common_content: common_content
      }
    end

    test "completes onboarding process successfully without identical files", %{
      conn: conn,
      user: user,
      template_repo: template_repo,
      sync_repo_common: sync_repo_common,
      sync_repo_distinct: sync_repo_distinct,
      folder: folder
    } do
      # --- Mocks for GitHub API ---
      GitHubMock
      |> stub(:client, fn user_param ->
        user_with_settings =
          if is_nil(user_param.settings),
            do: %{user_param | settings: %Settings{onboarding_completed: false}},
            else: user_param

        if user_with_settings.id == user.id, do: :test_client, else: raise("...")
      end)
      |> expect(:get_file_content, 2, fn :test_client, _owner, repo_name, path ->
        # With optimization, only common files are fetched
        # common.txt exists in sync_repo_common and sync_repo_distinct
        assert path == "common.txt"

        repo =
          cond do
            repo_name == sync_repo_common.name ->
              sync_repo_common

            repo_name == sync_repo_distinct.name ->
              sync_repo_distinct

            true ->
              raise "Mock :get_file_content called for unexpected repo: #{repo_name}, path: #{path}"
          end

        file = Enum.find(repo.files, &(&1.path == path))

        if file do
          {:ok, file.content, %{}}
        else
          {:error, :file_not_found}
        end
      end)

      # --- Start test ---
      {:ok, view, _html} =
        conn
        |> init_test_session(%{
          current_user_id: user.id,
          current_organization_id: user.default_organization_id
        })
        |> live(~p"/onboarding")

      # Step 1: Welcome
      assert has_element?(view, "li:first-child.step-primary")
      view |> element("button", "Next") |> render_click()

      # Step 2: Template Repository
      assert has_element?(view, "li:nth-child(2).step-primary")
      view |> element("#select_existing") |> render_click()

      # Verify repos are shown
      assert has_element?(view, "span", template_repo.full_name)
      assert has_element?(view, "span", sync_repo_common.full_name)
      assert has_element?(view, "span", sync_repo_distinct.full_name)

      # Verify folder structure (sync_repo_common is in folder)
      assert has_element?(view, "h3", folder.name)

      assert has_element?(
               view,
               "label[data-repo-id='#{sync_repo_common.id}']",
               sync_repo_common.full_name
             )

      # Others are unorganized
      assert has_element?(view, "h3", "Unorganized Repositories")

      assert has_element?(
               view,
               "label[data-repo-id='#{template_repo.id}']",
               template_repo.full_name
             )

      assert has_element?(
               view,
               "label[data-repo-id='#{sync_repo_distinct.id}']",
               sync_repo_distinct.full_name
             )

      # Select the template repository
      view
      |> element("input[value='#{template_repo.id}']")
      |> render_click()

      view |> element("button", "Next") |> render_click()

      # Step 3: Repository Sync
      assert has_element?(view, "li:nth-child(3).step-primary")

      # Select sync_repo_common AND sync_repo_distinct
      view |> element("button[phx-value-id='#{sync_repo_common.id}']") |> render_click()
      view |> element("button[phx-value-id='#{sync_repo_distinct.id}']") |> render_click()
      view |> element("button", "Next") |> render_click()

      # Step 4: Template Files
      assert has_element?(view, "h2", "Template Files")

      # Wait for common files to be loaded
      assert_eventually(fn ->
        refute has_element?(view, "p", "Loading common files...")
      end)

      # Verify the common file is found and listed
      assert_eventually(fn ->
        assert has_element?(view, "ul[data-testid='common-files-list']")
        assert has_element?(view, "span[data-testid='common-file-name']", "common.txt")
      end)

      view |> element("button", "Next") |> render_click()

      # Step 5: Summary
      assert has_element?(view, "li:nth-child(5).step-primary")
      assert has_element?(view, "h2", "Setup Summary")

      assert_eventually(
        fn ->
          html = render(view)
          assert html =~ "Congratulations! Your template repository is ready."
        end,
        2000
      )

      view = debug_html(view, "After finalization")

      # Verify summary information
      assert has_element?(view, "span.font-medium", template_repo.full_name)
      # Number of selected repos
      assert has_element?(view, "span.font-medium", "2")

      # Verify navigation links
      assert has_element?(view, "a", "Go to Template Repository")
      assert has_element?(view, "a", "Go to Dashboard")

      # Verify onboarding completion
      updated_user = Repobot.Repo.get(Repobot.Accounts.User, user.id)
      assert updated_user.settings.onboarding_completed

      # Verify the selected repository is now marked as a template
      updated_template_repo = Repobot.Repositories.get_repository!(template_repo.id)
      assert updated_template_repo.template == true
    end

    test "completes onboarding process successfully with identical files", %{
      conn: conn,
      user: user,
      template_repo: template_repo,
      sync_repo_common: sync_repo_common,
      sync_repo_distinct: sync_repo_distinct
    } do
      # Mock AI for tag inference
      Repobot.Test.AIMock
      |> stub(:infer_tags, fn _source_file, _organization -> {:ok, []} end)

      GitHubMock
      |> stub(:client, fn _user_param -> :test_client end)
      |> stub(:client, fn _owner, _repo -> :test_client end)
      |> expect(:get_file_content, 2, fn :test_client, _owner, repo_name, path ->
        # With optimization, only common files are fetched
        # common.txt exists in sync_repo_common and sync_repo_distinct
        assert path == "common.txt"

        repo =
          cond do
            repo_name == sync_repo_common.name ->
              sync_repo_common

            repo_name == sync_repo_distinct.name ->
              sync_repo_distinct

            true ->
              raise "Mock :get_file_content called for unexpected repo: #{repo_name}, path: #{path}"
          end

        file = Enum.find(repo.files, &(&1.path == path))

        if file do
          {:ok, file.content, %{}}
        else
          {:error, :file_not_found}
        end
      end)
      # Mock get_tree for repository refresh after GitHub commit
      |> expect(:get_tree, fn :test_client, _owner, repo_name ->
        if repo_name == template_repo.name do
          {:ok, [%{"path" => "common.txt", "type" => "blob"}]}
        else
          {:ok, []}
        end
      end)
      # Mock the GitHub API methods used by the Summary step
      |> expect(:get_ref, fn :test_client, _owner, repo_name, ref ->
        assert repo_name == template_repo.name
        # Assuming main is the default branch
        assert ref == "heads/main"
        {200, %{"object" => %{"sha" => "parent_commit_sha"}}, %{}}
      end)
      |> expect(:get_commit, fn :test_client, _owner, repo_name, sha ->
        assert repo_name == template_repo.name
        assert sha == "parent_commit_sha"
        {200, %{"tree" => %{"sha" => "base_tree_sha"}}, %{}}
      end)
      |> expect(:create_tree, fn :test_client, _owner, repo_name, tree_items, base_tree_sha ->
        assert repo_name == template_repo.name
        assert base_tree_sha == "base_tree_sha"
        assert length(tree_items) == 1

        # Verify the tree item for common.txt
        tree_item = List.first(tree_items)
        assert tree_item.path == "common.txt"
        assert tree_item.mode == "100644"
        assert tree_item.type == "blob"

        {201, %{"sha" => "new_tree_sha"}, %{}}
      end)
      |> expect(:create_commit, fn :test_client,
                                   _owner,
                                   repo_name,
                                   message,
                                   tree_sha,
                                   parent_shas ->
        assert repo_name == template_repo.name
        assert message =~ "Add 1 template files"
        assert tree_sha == "new_tree_sha"
        assert parent_shas == ["parent_commit_sha"]

        {201, %{"sha" => "new_commit_sha"}, %{}}
      end)
      |> expect(:update_reference, fn :test_client, _owner, repo_name, ref, sha, force ->
        assert repo_name == template_repo.name
        assert ref == "heads/main"
        assert sha == "new_commit_sha"
        assert force == false

        {200, %{}, %{}}
      end)

      # --- Start test ---
      {:ok, view, _html} =
        conn
        |> init_test_session(%{
          current_user_id: user.id,
          current_organization_id: user.default_organization_id
        })
        |> live(~p"/onboarding")

      # Step 1: Welcome
      assert has_element?(view, "li:first-child.step-primary")
      view |> element("button", "Next") |> render_click()

      # Step 2: Template Repository
      assert has_element?(view, "li:nth-child(2).step-primary")
      view |> element("#select_existing") |> render_click()

      # Select the template repository
      view
      |> element("input[value='#{template_repo.id}']")
      |> render_click()

      view |> element("button", "Next") |> render_click()

      # Step 3: Repository Sync
      assert has_element?(view, "li:nth-child(3).step-primary")

      # Select sync_repo_common AND sync_repo_distinct (both have common.txt)
      view |> element("button[phx-value-id='#{sync_repo_common.id}']") |> render_click()
      view |> element("button[phx-value-id='#{sync_repo_distinct.id}']") |> render_click()
      view |> element("button", "Next") |> render_click()

      # Step 4: Template Files
      assert has_element?(view, "h2", "Template Files")

      # Wait for common files to be loaded
      assert_eventually(fn ->
        refute has_element?(view, "p", "Loading common files...")
      end)

      # Verify the common file is found and listed
      assert_eventually(fn ->
        assert has_element?(view, "ul[data-testid='common-files-list']")
        assert has_element?(view, "span[data-testid='common-file-name']", "common.txt")
      end)

      # Select the common file
      view |> element("input[phx-value-path='common.txt']") |> render_click()

      view |> element("button", "Next") |> render_click()

      # Step 5: Summary - Wait for the step to become active
      assert_eventually(fn ->
        assert has_element?(view, "li:nth-child(5).step-primary")
      end)

      assert has_element?(view, "h2", "Setup Summary")

      # Wait for finalization to complete and onboarding to be marked as completed
      assert_eventually(fn ->
        updated_user = Repobot.Repo.get(Repobot.Accounts.User, user.id)
        assert updated_user.settings && updated_user.settings.onboarding_completed
      end)

      # Verify the selected repository is now marked as a template
      updated_template_repo = Repobot.Repositories.get_repository!(template_repo.id)
      assert updated_template_repo.template == true
    end

    test "creates new template repository during onboarding", %{
      conn: conn,
      user: user
    } do
      # --- Start test ---
      {:ok, view, _html} =
        conn
        |> init_test_session(%{
          current_user_id: user.id,
          current_organization_id: user.default_organization_id
        })
        |> live(~p"/onboarding")

      # Step 1: Welcome
      assert has_element?(view, "li:first-child.step-primary")
      view |> element("button", "Next") |> render_click()

      # Step 2: Template Repository - Create New Repository mode should be selected by default
      assert has_element?(view, "li:nth-child(2).step-primary")
      assert has_element?(view, "#create_new[checked]")

      # Enter repository name
      view
      |> element("input[name='template_repo_name']")
      |> render_keyup(%{value: "my-template-repo"})

      # Verify the create button appears
      assert has_element?(view, "button", "Create Repository on GitHub")

      # Click create repository button
      view |> element("button", "Create Repository on GitHub") |> render_click()

      # Verify loading state is shown
      assert has_element?(view, "div", "Waiting for repository creation...")

      # Simulate repository creation webhook event
      repo_data = %{
        name: "my-template-repo",
        full_name: "#{user.login}/my-template-repo",
        owner: user.login,
        private: false,
        description:
          "A template repository for standardizing project structure and best practices.",
        default_branch: "main"
      }

      # Create the repository in the database (simulating webhook)
      {:ok, _created_repo} =
        Repobot.Repositories.create_repository(%{
          name: repo_data.name,
          owner: repo_data.owner,
          full_name: repo_data.full_name,
          private: repo_data.private,
          data: %{
            "name" => repo_data.name,
            "full_name" => repo_data.full_name,
            "owner" => %{"login" => repo_data.owner},
            "private" => repo_data.private,
            "description" => repo_data.description,
            "default_branch" => repo_data.default_branch
          },
          organization_id: user.default_organization_id
        })

      # Send repository creation event to the LiveView
      send(view.pid, {:repository_created, repo_data})

      # Verify we automatically proceed to the next step
      assert_eventually(fn ->
        assert has_element?(view, "li:nth-child(3).step-primary")
        assert has_element?(view, "h2", "Repository Sync")
      end)

      # Verify the repository was created in the database
      created_repo = Repobot.Repositories.get_repository_by(full_name: repo_data.full_name)
      assert created_repo != nil
      assert created_repo.name == "my-template-repo"
      assert created_repo.owner == user.login
    end

    test "marks repository as template and handles folder associations during onboarding", %{
      conn: conn,
      user: user,
      sync_repo_common: sync_repo_common,
      sync_repo_distinct: sync_repo_distinct,
      folder: folder
    } do
      # Put the repository in a folder first
      {:ok, repo_in_folder} =
        Repobot.Repositories.update_repository(sync_repo_common, %{folder_id: folder.id})

      # Verify it's not a template initially
      refute repo_in_folder.template

      # Mock GitHub API
      GitHubMock
      |> stub(:client, fn _user_param -> :test_client end)
      |> stub(:client, fn _owner, _repo -> :test_client end)
      |> expect(:get_file_content, 0, fn :test_client, _owner, _repo_name, _path ->
        {:ok, "test content", %{}}
      end)

      {:ok, view, _html} =
        conn
        |> init_test_session(%{
          current_user_id: user.id,
          current_organization_id: user.default_organization_id
        })
        |> live(~p"/onboarding")

      # Navigate through onboarding steps
      # Welcome -> Template Repository
      view |> element("button", "Next") |> render_click()
      # Select existing repo mode
      view |> element("#select_existing") |> render_click()
      # Select the repo
      view |> element("input[value='#{repo_in_folder.id}']") |> render_click()
      # Template Repository -> Repository Sync
      view |> element("button", "Next") |> render_click()
      # Select a repository for sync (required for template files step)
      view |> element("button[phx-value-id='#{sync_repo_distinct.id}']") |> render_click()
      # Repository Sync -> Template Files
      view |> element("button", "Next") |> render_click()
      # Template Files -> Summary (skip file selection)
      view |> element("button", "Next") |> render_click()

      # Wait for finalization
      assert_eventually(fn ->
        updated_user = Repobot.Repo.get(Repobot.Accounts.User, user.id)
        assert updated_user.settings && updated_user.settings.onboarding_completed
      end)

      # Verify the repository is now marked as a template
      updated_repo = Repobot.Repositories.get_repository!(repo_in_folder.id)
      assert updated_repo.template == true

      # Verify folder_id is cleared (templates don't have primary folders)
      assert updated_repo.folder_id == nil

      # Verify the repository is now associated with the folder via template_folders
      updated_repo_with_folders = Repobot.Repo.preload(updated_repo, :template_folders)
      assert length(updated_repo_with_folders.template_folders) == 1
      assert hd(updated_repo_with_folders.template_folders).id == folder.id
    end

    test "adds template repository to all target folders during onboarding", %{
      conn: conn,
      user: user,
      sync_repo_common: sync_repo_common,
      sync_repo_distinct: sync_repo_distinct,
      folder: folder
    } do
      # Create a second folder
      {:ok, folder2} =
        Repobot.Folders.create_folder(%{
          name: "JavaScript",
          organization_id: user.default_organization_id
        })

      # Put repositories in different folders
      {:ok, repo_in_folder1} =
        Repobot.Repositories.update_repository(sync_repo_common, %{folder_id: folder.id})

      {:ok, repo_in_folder2} =
        Repobot.Repositories.update_repository(sync_repo_distinct, %{folder_id: folder2.id})

      # Create a template repository (not in any folder initially)
      {:ok, template_repo} =
        Repobot.Repositories.create_repository(%{
          name: "template-repo",
          owner: user.login,
          full_name: "#{user.login}/template-repo",
          organization_id: user.default_organization_id,
          template: false
        })

      # Mock GitHub API - we need to mock both get_tree and get_file_content calls
      GitHubMock
      |> stub(:client, fn _user_param -> :test_client end)
      |> stub(:client, fn _owner, _repo -> :test_client end)
      |> stub(:get_tree, fn :test_client, _owner, _repo ->
        {:ok, [%{"path" => "README.md", "type" => "blob"}]}
      end)
      |> stub(:get_file_content, fn :test_client, _owner, _repo_name, _path ->
        {:ok, "test content", %{}}
      end)

      {:ok, view, _html} =
        conn
        |> init_test_session(%{
          current_user_id: user.id,
          current_organization_id: user.default_organization_id
        })
        |> live(~p"/onboarding")

      # Navigate through onboarding steps
      # Welcome -> Template Repository
      view |> element("button", "Next") |> render_click()
      # Select existing repo mode
      view |> element("#select_existing") |> render_click()
      # Select the template repo
      view |> element("input[value='#{template_repo.id}']") |> render_click()
      # Template Repository -> Repository Sync
      view |> element("button", "Next") |> render_click()
      # Select repositories from both folders
      view |> element("button[phx-value-id='#{repo_in_folder1.id}']") |> render_click()
      view |> element("button[phx-value-id='#{repo_in_folder2.id}']") |> render_click()
      # Repository Sync -> Template Files
      view |> element("button", "Next") |> render_click()
      # Template Files -> Summary (skip file selection)
      view |> element("button", "Next") |> render_click()

      # Wait for finalization
      assert_eventually(fn ->
        updated_user = Repobot.Repo.get(Repobot.Accounts.User, user.id)
        assert updated_user.settings && updated_user.settings.onboarding_completed
      end)

      # Verify the template repository is now marked as a template
      updated_template_repo = Repobot.Repositories.get_repository!(template_repo.id)
      assert updated_template_repo.template == true

      # Verify the template repository is associated with both target folders
      updated_template_repo_with_folders =
        Repobot.Repo.preload(updated_template_repo, :template_folders)

      folder_ids = Enum.map(updated_template_repo_with_folders.template_folders, & &1.id)

      assert length(updated_template_repo_with_folders.template_folders) == 2
      assert folder.id in folder_ids
      assert folder2.id in folder_ids
    end

    test "associates selected common files with target folders during onboarding", %{
      conn: conn,
      user: user,
      template_repo: template_repo,
      sync_repo_common: sync_repo_common,
      sync_repo_distinct: sync_repo_distinct,
      folder: folder,
      common_content: common_content
    } do
      # Create a second folder
      {:ok, folder2} =
        Repobot.Folders.create_folder(%{
          name: "JavaScript",
          organization_id: user.default_organization_id
        })

      # Put sync_repo_distinct in the second folder
      {:ok, repo_in_folder2} =
        Repobot.Repositories.update_repository(sync_repo_distinct, %{folder_id: folder2.id})

      # Mock AI for tag inference
      Repobot.Test.AIMock
      |> stub(:infer_tags, fn _source_file, _organization -> {:ok, []} end)

      # Mock GitHub API calls
      GitHubMock
      |> stub(:client, fn _user_param -> :test_client end)
      |> stub(:client, fn _owner, _repo -> :test_client end)
      |> expect(:get_file_content, 2, fn :test_client, _owner, repo_name, path ->
        # With optimization, only common files are fetched
        # common.txt exists in sync_repo_common and repo_in_folder2 (sync_repo_distinct)
        assert path == "common.txt"

        repo =
          cond do
            repo_name == sync_repo_common.name ->
              sync_repo_common

            repo_name == repo_in_folder2.name ->
              repo_in_folder2

            true ->
              raise "Mock :get_file_content called for unexpected repo: #{repo_name}, path: #{path}"
          end

        file = Enum.find(repo.files, &(&1.path == path))

        if file do
          {:ok, file.content, %{}}
        else
          {:error, :file_not_found}
        end
      end)
      # Mock get_tree for repository refresh after GitHub commit
      |> expect(:get_tree, fn :test_client, _owner, repo_name ->
        if repo_name == template_repo.name do
          {:ok, [%{"path" => "common.txt", "type" => "blob"}]}
        else
          {:ok, []}
        end
      end)
      # Mock GitHub API methods for creating files in template repo
      |> expect(:get_ref, fn :test_client, _owner, repo_name, ref ->
        assert repo_name == template_repo.name
        assert ref == "heads/main"
        {200, %{"object" => %{"sha" => "parent_commit_sha"}}, %{}}
      end)
      |> expect(:get_commit, fn :test_client, _owner, repo_name, sha ->
        assert repo_name == template_repo.name
        assert sha == "parent_commit_sha"
        {200, %{"tree" => %{"sha" => "base_tree_sha"}}, %{}}
      end)
      |> expect(:create_tree, fn :test_client, _owner, repo_name, tree_items, base_tree_sha ->
        assert repo_name == template_repo.name
        assert base_tree_sha == "base_tree_sha"
        assert length(tree_items) == 1

        # Verify the tree item for common.txt
        tree_item = List.first(tree_items)
        assert tree_item.path == "common.txt"
        assert tree_item.content == common_content

        {201, %{"sha" => "new_tree_sha"}, %{}}
      end)
      |> expect(:create_commit, fn :test_client,
                                   _owner,
                                   repo_name,
                                   message,
                                   tree_sha,
                                   parent_shas ->
        assert repo_name == template_repo.name
        assert message =~ "Add 1 template files"
        assert tree_sha == "new_tree_sha"
        assert parent_shas == ["parent_commit_sha"]

        {201, %{"sha" => "new_commit_sha"}, %{}}
      end)
      |> expect(:update_reference, fn :test_client, _owner, repo_name, ref, sha, force ->
        assert repo_name == template_repo.name
        assert ref == "heads/main"
        assert sha == "new_commit_sha"
        assert force == false

        {200, %{}, %{}}
      end)

      {:ok, view, _html} =
        conn
        |> init_test_session(%{
          current_user_id: user.id,
          current_organization_id: user.default_organization_id
        })
        |> live(~p"/onboarding")

      # Navigate through onboarding steps
      # Welcome -> Template Repository
      view |> element("button", "Next") |> render_click()
      view |> element("#select_existing") |> render_click()
      view |> element("input[value='#{template_repo.id}']") |> render_click()
      # Template Repository -> Repository Sync
      view |> element("button", "Next") |> render_click()
      # Select repositories from both folders
      view |> element("button[phx-value-id='#{sync_repo_common.id}']") |> render_click()
      view |> element("button[phx-value-id='#{repo_in_folder2.id}']") |> render_click()
      # Repository Sync -> Template Files
      view |> element("button", "Next") |> render_click()

      # Wait for common files to be loaded
      assert_eventually(fn ->
        refute has_element?(view, "p", "Loading common files...")
      end)

      # Select the common file
      assert_eventually(fn ->
        assert has_element?(view, "span[data-testid='common-file-name']", "common.txt")
      end)

      view |> element("input[phx-value-path='common.txt']") |> render_click()

      # Template Files -> Summary
      view |> element("button", "Next") |> render_click()

      # Wait for finalization to complete
      assert_eventually(fn ->
        updated_user = Repobot.Repo.get(Repobot.Accounts.User, user.id)
        assert updated_user.settings && updated_user.settings.onboarding_completed
      end)

      # Verify the source file was created
      user_with_org = Repobot.Repo.preload(user, :default_organization)

      source_files =
        Repobot.SourceFiles.list_source_files(user_with_org, user_with_org.default_organization)

      common_source_file = Enum.find(source_files, &(&1.name == "common.txt"))
      assert common_source_file != nil
      assert common_source_file.content == common_content

      # Verify the source file is associated with both target folders
      source_file_with_folders = Repobot.Repo.preload(common_source_file, :folders)
      folder_ids = Enum.map(source_file_with_folders.folders, & &1.id)

      assert length(source_file_with_folders.folders) == 2
      assert folder.id in folder_ids
      assert folder2.id in folder_ids
    end

    test "handles empty template repository during onboarding", %{
      conn: conn,
      user: user,
      sync_repo_common: sync_repo_common,
      sync_repo_distinct: sync_repo_distinct,
      common_content: common_content
    } do
      # Create an empty template repository
      {:ok, empty_template_repo} =
        Repobot.Repositories.create_repository(%{
          name: "empty-template-repo",
          owner: user.login,
          full_name: "#{user.login}/empty-template-repo",
          organization_id: user.default_organization_id,
          template: false,
          data: %{
            "name" => "empty-template-repo",
            "full_name" => "#{user.login}/empty-template-repo",
            "owner" => %{"login" => user.login},
            "private" => false,
            "default_branch" => "main"
          }
        })

      # Mock AI for tag inference
      Repobot.Test.AIMock
      |> stub(:infer_tags, fn _source_file, _organization -> {:ok, []} end)

      # Mock GitHub API calls
      GitHubMock
      |> stub(:client, fn _user_param -> :test_client end)
      |> stub(:client, fn _owner, _repo -> :test_client end)
      # Mock get_tree - this will be called multiple times during the test
      # First during template files step (empty repo has no files)
      # Later during repository refresh after GitHub commit (repo has the file we created)
      |> expect(:get_tree, 2, fn :test_client, _owner, repo_name ->
        if repo_name == empty_template_repo.name do
          # Empty repository has no files initially
          {:ok, []}
        else
          # For sync repositories, return their files
          repo =
            cond do
              repo_name == sync_repo_common.name -> sync_repo_common
              repo_name == sync_repo_distinct.name -> sync_repo_distinct
              true -> nil
            end

          if repo do
            files =
              Enum.map(repo.files, fn file ->
                %{"path" => file.path, "type" => "blob", "size" => byte_size(file.content || "")}
              end)

            {:ok, files}
          else
            {:ok, []}
          end
        end
      end)
      |> expect(:get_file_content, 2, fn :test_client, _owner, repo_name, path ->
        assert path == "common.txt"

        repo =
          cond do
            repo_name == sync_repo_common.name ->
              sync_repo_common

            repo_name == sync_repo_distinct.name ->
              sync_repo_distinct

            true ->
              raise "Mock :get_file_content called for unexpected repo: #{repo_name}, path: #{path}"
          end

        file = Enum.find(repo.files, &(&1.path == path))

        if file do
          {:ok, file.content, %{}}
        else
          {:error, :file_not_found}
        end
      end)

      # Mock GitHub API methods for empty repository (409 error on get_ref)
      |> expect(:get_ref, fn :test_client, _owner, repo_name, ref ->
        assert repo_name == empty_template_repo.name
        assert ref == "heads/main"
        # Return 409 error indicating empty repository
        {409, %{"message" => "Git Repository is empty."}, %{}}
      end)
      # For empty repository, we now use create_file instead of Git database API
      |> expect(:create_file, fn :test_client,
                                 _owner,
                                 repo_name,
                                 path,
                                 content,
                                 message,
                                 branch ->
        assert repo_name == empty_template_repo.name
        assert path == "common.txt"
        assert message == "Add common.txt via Repobot"
        # No specific branch for empty repo
        assert branch == nil

        # Verify the content is base64 encoded
        decoded_content = Base.decode64!(content)
        assert decoded_content == common_content

        {201, %{"content" => %{"sha" => "file_sha"}}, %{}}
      end)

      {:ok, view, _html} =
        conn
        |> init_test_session(%{
          current_user_id: user.id,
          current_organization_id: user.default_organization_id
        })
        |> live(~p"/onboarding")

      # Step 1: Welcome
      assert has_element?(view, "li:first-child.step-primary")
      view |> element("button", "Next") |> render_click()

      # Step 2: Template Repository
      assert has_element?(view, "li:nth-child(2).step-primary")
      view |> element("#select_existing") |> render_click()

      # Select the empty template repository
      view
      |> element("input[value='#{empty_template_repo.id}']")
      |> render_click()

      view |> element("button", "Next") |> render_click()

      # Step 3: Repository Sync
      assert has_element?(view, "li:nth-child(3).step-primary")

      # Select sync repositories
      view |> element("button[phx-value-id='#{sync_repo_common.id}']") |> render_click()
      view |> element("button[phx-value-id='#{sync_repo_distinct.id}']") |> render_click()
      view |> element("button", "Next") |> render_click()

      # Step 4: Template Files
      assert has_element?(view, "h2", "Template Files")

      # Wait for common files to be loaded
      assert_eventually(fn ->
        refute has_element?(view, "p", "Loading common files...")
      end)

      # Verify the common file is found and listed
      assert_eventually(fn ->
        assert has_element?(view, "ul[data-testid='common-files-list']")
        assert has_element?(view, "span[data-testid='common-file-name']", "common.txt")
      end)

      # Select the common file
      view |> element("input[phx-value-path='common.txt']") |> render_click()

      view |> element("button", "Next") |> render_click()

      # Step 5: Summary - Wait for the step to become active
      assert_eventually(fn ->
        assert has_element?(view, "li:nth-child(5).step-primary")
      end)

      assert has_element?(view, "h2", "Setup Summary")

      # Wait for finalization to complete and onboarding to be marked as completed
      assert_eventually(fn ->
        updated_user = Repobot.Repo.get(Repobot.Accounts.User, user.id)
        assert updated_user.settings && updated_user.settings.onboarding_completed
      end)

      # Verify the selected repository is now marked as a template
      updated_template_repo = Repobot.Repositories.get_repository!(empty_template_repo.id)
      assert updated_template_repo.template == true
    end
  end
end
