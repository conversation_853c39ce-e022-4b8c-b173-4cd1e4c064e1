defmodule Repobot.GitHubTreeEfficiencyTest do
  use Repobot.DataCase
  import Mox
  use Repobot.Test.Fixtures

  alias Repobot.{GitHub, Repository, RepositoryFiles}
  alias Repobot.Test.GitHubMock

  setup :verify_on_exit!
  setup :set_mox_from_context

  describe "efficient tree loading" do
    test "get_tree_efficient uses Git Trees API with recursive parameter" do
      # Mock the repository info call
      expect(GitHubMock, :get_repo, fn _client, "owner", "repo" ->
        {200, %{"default_branch" => "main"}, %{}}
      end)

      # Mock the branch ref call
      expect(GitHubMock, :get_ref, fn _client, "owner", "repo", "heads/main" ->
        {200, %{"object" => %{"sha" => "commit_sha_123"}}, %{}}
      end)

      # Mock the commit call to get tree SHA
      expect(GitHubMock, :get_commit, fn _client, "owner", "repo", "commit_sha_123" ->
        {200, %{"tree" => %{"sha" => "tree_sha_456"}}, %{}}
      end)

      # Mock the Git Trees API call with recursive=1
      expect(Tentacat, :get, fn "repos/owner/repo/git/trees/tree_sha_456",
                                _client,
                                [{"recursive", "1"}] ->
        {200,
         %{
           "tree" => [
             %{
               "path" => "README.md",
               "type" => "blob",
               "size" => 1024,
               "sha" => "file_sha_1"
             },
             %{
               "path" => "src/main.ex",
               "type" => "blob",
               "size" => 2048,
               "sha" => "file_sha_2"
             },
             %{
               "path" => "src/utils.ex",
               "type" => "blob",
               "size" => 512,
               "sha" => "file_sha_3"
             }
           ],
           "truncated" => false
         }, %HTTPoison.Response{headers: [{"X-RateLimit-Remaining", "4999"}]}}
      end)

      client = %{}

      result = GitHub.get_tree_efficient(client, "owner", "repo")

      assert {:ok, tree} = result
      assert length(tree) == 3

      # Verify the tree items are properly formatted
      assert Enum.any?(tree, fn item ->
               item["path"] == "README.md" and
                 item["type"] == "file" and
                 item["name"] == "README.md"
             end)

      assert Enum.any?(tree, fn item ->
               item["path"] == "src/main.ex" and
                 item["type"] == "file" and
                 item["name"] == "main.ex"
             end)
    end

    test "sync_repository_files uses efficient tree loading and caches tree SHA" do
      user = create_user()

      repository =
        create_repository(%{
          owner: "owner",
          name: "repo",
          organization_id: user.default_organization_id,
          tree_sha: nil,
          tree_updated_at: nil
        })

      # Mock getting current tree SHA
      expect(GitHubMock, :get_repo, fn _client, "owner", "repo" ->
        {200, %{"default_branch" => "main"}, %{}}
      end)

      expect(GitHubMock, :get_ref, fn _client, "owner", "repo", "heads/main" ->
        {200, %{"object" => %{"sha" => "commit_sha_123"}}, %{}}
      end)

      expect(GitHubMock, :get_commit, fn _client, "owner", "repo", "commit_sha_123" ->
        {200, %{"tree" => %{"sha" => "new_tree_sha_789"}}, %{}}
      end)

      # Mock efficient tree fetching
      expect(GitHubMock, :get_tree_efficient, fn _client, "owner", "repo", "new_tree_sha_789" ->
        {:ok,
         [
           %{
             "path" => "README.md",
             "type" => "file",
             "size" => 1024,
             "sha" => "file_sha_1",
             "name" => "README.md"
           }
         ]}
      end)

      # Mock client creation
      expect(GitHubMock, :client, fn _user ->
        %{}
      end)

      result = RepositoryFiles.sync_repository_files(repository, user)

      assert {:ok, _files} = result

      # Verify the repository was updated with the new tree SHA
      updated_repo = Repo.get!(Repository, repository.id)
      assert updated_repo.tree_sha == "new_tree_sha_789"
      assert not is_nil(updated_repo.tree_updated_at)
    end

    test "sync_repository_files skips sync when tree SHA is unchanged" do
      user = create_user()

      repository =
        create_repository(%{
          owner: "owner",
          name: "repo",
          organization_id: user.default_organization_id,
          tree_sha: "existing_tree_sha",
          tree_updated_at: DateTime.utc_now()
        })

      # Mock getting current tree SHA (same as existing)
      expect(GitHubMock, :get_repo, fn _client, "owner", "repo" ->
        {200, %{"default_branch" => "main"}, %{}}
      end)

      expect(GitHubMock, :get_ref, fn _client, "owner", "repo", "heads/main" ->
        {200, %{"object" => %{"sha" => "commit_sha_123"}}, %{}}
      end)

      expect(GitHubMock, :get_commit, fn _client, "owner", "repo", "commit_sha_123" ->
        {200, %{"tree" => %{"sha" => "existing_tree_sha"}}, %{}}
      end)

      # Mock client creation
      expect(GitHubMock, :client, fn _user ->
        %{}
      end)

      # Should NOT call get_tree_efficient since tree SHA is unchanged

      result = RepositoryFiles.sync_repository_files(repository, user)

      assert {:ok, []} = result
    end
  end
end
